#!/usr/bin/env python3
"""
Quick test script to validate Streamlit application imports
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required imports work"""
    try:
        # Test core imports
        from models.interview import EvaluationR<PERSON><PERSON>, Seniority, QuestionEvaluation
        print("✅ Interview models imported successfully")
        
        from models.llm import inference_with_fallback, get_related_class_definitions
        print("✅ LLM models imported successfully")
        
        from langchain_core.messages import HumanMessage
        print("✅ LangChain imports successful")
        
        from config.config import MODELS_CONFIG
        print("✅ Config imports successful")
        
        # Test Streamlit import
        try:
            import streamlit as st
            print("✅ Streamlit imported successfully")
        except ImportError:
            print("⚠️  Streamlit not installed. Run: pip install streamlit")
        
        print("\n🎉 All imports successful! Ready to run Streamlit app.")
        print("Run: streamlit run streamlit_test_evaluate_interview.py")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the project root directory")
        return False
    
    return True

if __name__ == "__main__":
    test_imports()
