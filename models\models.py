from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, field_validator


class ProjectBase(BaseModel):
    client_name: str
    name: str
    description: Optional[str] = None
    status: Optional[bool] = True


class ProjectCreate(ProjectBase):
    pass


class Project(ProjectBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PositionBase(BaseModel):
    proj_id: str
    position_info: dict
    top_candidates: Optional[List[dict]] = None
    question_answers: Optional[List[dict]] = None


class PositionCreate(PositionBase):
    pass


class PositionUpdate(PositionBase):
    id: Optional[str] = None


class PositionRawCreate (BaseModel):
    proj_id: str
    position_description: str
    external_id: Optional[str] = None


class Position(PositionBase):
    id: str
    last_matching: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class QuestionsBase(BaseModel):
    id: str
    position_id: str
    data: dict
    allow_regeneration: bool
    created_at: datetime
    created_by: Optional[str] = None
    updated_at: datetime
    updated_by: Optional[str] = None


class SingleQuestions(QuestionsBase):
    pass


class PositionFilters(BaseModel):
    stage: Optional[bool] = None  # True for open, False for closed
    search_term: Optional[str] = None  # Search term for position info
    client_name: Optional[str] = None  # e.g. "Arroyo"
    location: Optional[str] = None  # e.g. "LATAM"
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None

    @field_validator('created_from', 'created_to', mode='before')
    @classmethod
    def parse_empty_string_as_none(cls, v):
        if v == '':
            return None
        return v
