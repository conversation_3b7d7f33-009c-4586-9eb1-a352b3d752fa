FROM python:3.11-slim-bullseye

WORKDIR /app

# System dependencies + wkhtmltopdf available in bullseye
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev build-essential poppler-utils wkhtmltopdf libreoffice \
    fontconfig fonts-dejavu xfonts-75dpi \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# check if wkhtmltopdf is installed correctly
RUN wkhtmltopdf -V

# Create a non-root user
RUN useradd --create-home appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8080
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--log-level", "info"]


