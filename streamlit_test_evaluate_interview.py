#!/usr/bin/env python3
"""
Streamlit Test Application for Enhanced Interview Evaluation

This application allows testing the improved evaluate_interview function
with enhanced prompt integration for feedback comments and transcript data.

Usage:
    pip install streamlit
    streamlit run streamlit_test_evaluate_interview.py
"""

import streamlit as st
import json
import sys
import os
from datetime import datetime
from uuid import uuid4
from typing import Dict, Any, Optional

# Add the project root to Python path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from models.interview import EvaluationResult, Seniority, QuestionEvaluation
    from models.llm import inference_with_fallback, get_related_class_definitions
    from langchain_core.messages import HumanMessage
    from config.config import MODELS_CONFIG
except ImportError as e:
    st.error(f"Import error: {e}")
    st.error("Make sure you're running this from the project root directory")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="Interview Evaluation Tester",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

st.title("🎯 Enhanced Interview Evaluation Tester")
st.markdown("Test the improved `evaluate_interview` function with enhanced prompt integration")

# Sidebar for configuration
st.sidebar.header("Configuration")
test_mode = st.sidebar.selectbox(
    "Test Mode",
    ["Mock LLM (Fast)", "Real LLM (Requires API)"],
    help="Mock mode for quick testing, Real mode calls actual LLM"
)

# Mock data generation functions
def generate_mock_questions():
    """Generate mock interview questions data"""
    return {
        "questions": [
            {
                "question_number": 1,
                "question": "Explain the difference between synchronous and asynchronous programming in Python",
                "senior_answer": "Comprehensive understanding of event loops, coroutines, async/await syntax, and performance implications in high-concurrency scenarios",
                "mid_answer": "Good grasp of async/await, basic understanding of when to use asynchronous programming",
                "junior_answer": "Basic knowledge of async/await syntax, limited practical experience"
            },
            {
                "question_number": 2,
                "question": "How would you optimize a slow database query?",
                "senior_answer": "Multi-faceted approach including indexing strategies, query plan analysis, database-specific optimizations, caching layers",
                "mid_answer": "Understanding of indexing, basic query optimization techniques, some experience with EXPLAIN plans",
                "junior_answer": "Basic knowledge of indexes, limited experience with query optimization"
            },
            {
                "question_number": 3,
                "question": "Describe your approach to handling errors in a distributed system",
                "senior_answer": "Circuit breakers, retry strategies, graceful degradation, monitoring, distributed tracing",
                "mid_answer": "Understanding of try-catch blocks, basic retry mechanisms, some knowledge of logging",
                "junior_answer": "Basic error handling with try-catch, limited experience with distributed systems"
            }
        ]
    }

def generate_mock_answers():
    """Generate mock candidate answers"""
    return {
        "answers": [
            "Asynchronous programming allows multiple operations to run concurrently without blocking. In Python, we use async/await syntax with asyncio. It's great for I/O-bound operations like API calls or database queries.",
            "I would start by looking at the query execution plan using EXPLAIN. Then I'd check if proper indexes exist on the columns used in WHERE clauses. I might also consider query restructuring or adding database-specific optimizations.",
            "Error handling in distributed systems requires multiple strategies. I implement retry logic with exponential backoff, use circuit breakers to prevent cascade failures, and ensure proper logging and monitoring for debugging."
        ]
    }

# Main content area
col1, col2 = st.columns(2)

with col1:
    st.header("📝 Test Data Input")
    
    # Expected Questions Section
    st.subheader("Expected Questions")
    if st.button("Load Sample Questions"):
        st.session_state.questions_data = generate_mock_questions()
    
    questions_json = st.text_area(
        "Questions JSON",
        value=json.dumps(st.session_state.get('questions_data', generate_mock_questions()), indent=2),
        height=200,
        help="JSON structure with interview questions and expected answers by seniority level"
    )
    
    # Actual Answers Section
    st.subheader("Candidate Answers")
    if st.button("Load Sample Answers"):
        st.session_state.answers_data = generate_mock_answers()
    
    answers_json = st.text_area(
        "Answers JSON",
        value=json.dumps(st.session_state.get('answers_data', generate_mock_answers()), indent=2),
        height=150,
        help="JSON structure with candidate's actual answers"
    )

with col2:
    st.header("🔧 Enhanced Data")
    
    # Feedback Comments Section
    st.subheader("Technical Feedback Comments")
    feedback_preset = st.selectbox(
        "Feedback Preset",
        ["Custom", "Positive Technical", "Critical Technical", "Mixed Assessment"],
        help="Choose a preset or create custom feedback"
    )
    
    if feedback_preset == "Positive Technical":
        default_feedback = {
            "technical_skills": "Excelente dominio técnico, demuestra experiencia sólida",
            "problem_solving": "Enfoque estructurado para resolver problemas complejos",
            "communication": "Explica conceptos técnicos de manera clara y precisa",
            "overall_impression": "Candidato muy fuerte, recomendado para posición senior"
        }
    elif feedback_preset == "Critical Technical":
        default_feedback = {
            "technical_skills": "Conocimiento superficial, respuestas vagas",
            "problem_solving": "Dificultades para abordar problemas complejos",
            "communication": "Explicaciones confusas, falta claridad técnica",
            "overall_impression": "Necesita más experiencia, nivel junior"
        }
    elif feedback_preset == "Mixed Assessment":
        default_feedback = {
            "technical_skills": "Buen conocimiento base, algunas áreas por mejorar",
            "problem_solving": "Enfoque correcto pero falta profundidad",
            "communication": "Comunicación clara en temas conocidos",
            "overall_impression": "Candidato sólido para nivel mid, con potencial de crecimiento"
        }
    else:
        default_feedback = {}
    
    feedback_json = st.text_area(
        "Feedback Comments JSON",
        value=json.dumps(default_feedback, indent=2, ensure_ascii=False),
        height=200,
        help="Technical feedback comments from the interviewer"
    )
    
    # Transcript Section
    st.subheader("Interview Transcript Excerpt")
    transcript_preset = st.selectbox(
        "Transcript Preset",
        ["Custom", "Detailed Technical", "Basic Responses", "Comprehensive Discussion"]
    )
    
    if transcript_preset == "Detailed Technical":
        default_transcript = """Interviewer: Can you explain async programming in Python?
Candidate: Sure! Async programming allows us to handle multiple operations concurrently. In Python, we use the asyncio library with async/await syntax. For example, when making API calls, instead of waiting for each call to complete, we can initiate multiple calls and await their results together using asyncio.gather(). This is particularly useful for I/O-bound operations where we're waiting for external resources.

Interviewer: How would you handle errors in this context?
Candidate: Great question! In async contexts, we need to be careful about exception handling. We can use try-except blocks around await statements, but also consider using asyncio.create_task() with proper exception handling. For distributed systems, I'd implement circuit breakers and retry logic with exponential backoff."""
    elif transcript_preset == "Basic Responses":
        default_transcript = """Interviewer: Tell me about async programming.
Candidate: Async programming is when you don't wait for things to finish. You use async and await in Python.

Interviewer: Can you give an example?
Candidate: Um, like when you make API calls, you can do multiple at once instead of one by one."""
    elif transcript_preset == "Comprehensive Discussion":
        default_transcript = """Interviewer: Let's discuss database optimization strategies.
Candidate: Database optimization is multi-layered. First, I analyze the query execution plan using EXPLAIN or EXPLAIN ANALYZE. I look for sequential scans that could benefit from indexes, check for proper join strategies, and identify bottlenecks. Beyond indexing, I consider query restructuring, materialized views for complex aggregations, and connection pooling. For high-traffic applications, I implement caching layers using Redis or Memcached, and consider read replicas for scaling read operations.

Interviewer: How do you handle schema changes in production?
Candidate: Schema migrations require careful planning. I use migration tools like Alembic for SQLAlchemy, ensure backward compatibility during deployments, and implement blue-green deployment strategies for zero-downtime updates. I also maintain comprehensive backup strategies and test migrations in staging environments that mirror production data volumes."""
    else:
        default_transcript = ""
    
    transcript_text = st.text_area(
        "Transcript Text",
        value=default_transcript,
        height=200,
        help="Interview conversation transcript"
    )

# Test execution section
st.header("🚀 Run Evaluation Test")

if st.button("Run Enhanced Evaluation Test", type="primary"):
    try:
        # Parse input data
        questions_data = json.loads(questions_json)
        answers_data = json.loads(answers_json) if isinstance(json.loads(answers_json), dict) else {"answers": json.loads(answers_json)}
        feedback_data = json.loads(feedback_json) if feedback_json.strip() else None
        
        # Prepare evaluation context (simulating the enhanced function)
        evaluation_context = {
            'expected': questions_data,
            'actual': answers_data
        }
        
        # Add enhanced data if available
        if feedback_data:
            evaluation_context['feedback_comments'] = feedback_data
        
        if transcript_text.strip():
            transcript_excerpt = transcript_text[:1000] + "..." if len(transcript_text) > 1000 else transcript_text
            evaluation_context['transcript_excerpt'] = transcript_excerpt
        
        # Enhanced task prompt (from the improved function)
        task_prompt = (
            "Evaluate the candidate's responses against expected senior/mid/junior levels by integrating all available information. "
            "For each question, identify 'detected_seniority' (senior|mid|junior) and provide an explanation. "
            "Additionally, determine the 'overall_seniority' and calculate the 'percentage_of_match'. "
            "Deliver a JSON output conforming to the specified schema."
            "CRITICAL INTEGRATION REQUIREMENTS:\n"
            "• PRIORITIZE expert manual feedback comments where available, as they offer critical human insights.\n"
            "• Utilize the transcript to understand the conversation flow and the candidate's reasoning.\n"
            "• Cross-reference responses with feedback comments to uncover patterns and insights.\n"
            "• Link specific strengths/weaknesses noted in feedback comments to supporting evidence in the Q&A.\n"
            "• Acknowledge and explain discrepancies between comments and extracted answers.\n"
            "• Conclude with an 'overall_seniority' based on a holistic analysis of all data sources.\n"
            "• Include a detailed explanation of how comments, transcript, and answers were integrated.\n"
            "• Ensure the output is a valid JSON object with the correct structure and syntax.\n"
        )
        
        # Display the context being sent to LLM
        st.subheader("📤 Data Sent to LLM")
        st.json(evaluation_context)
        
        # Mock or real LLM call
        if test_mode == "Mock LLM (Fast)":
            # Generate mock result based on feedback sentiment
            if feedback_data:
                feedback_text = json.dumps(feedback_data, ensure_ascii=False).lower()
                if "excelente" in feedback_text or "fuerte" in feedback_text or "senior" in feedback_text:
                    overall_seniority = "senior"
                    percentage = 85.0
                elif "superficial" in feedback_text or "junior" in feedback_text or "dificultades" in feedback_text:
                    overall_seniority = "junior"
                    percentage = 45.0
                else:
                    overall_seniority = "mid"
                    percentage = 70.0
            else:
                overall_seniority = "mid"
                percentage = 65.0
            
            # Create mock evaluation result
            mock_result = {
                "overall_seniority": overall_seniority,
                "per_question": [
                    {
                        "question_number": i+1,
                        "expected_seniority": "senior",
                        "detected_seniority": overall_seniority,
                        "explanation": f"Based on the integrated analysis of feedback comments and transcript, the candidate demonstrates {overall_seniority}-level understanding of this topic."
                    }
                    for i in range(len(questions_data.get("questions", [])))
                ],
                "percentage_of_match": percentage,
                "explanation": f"Integrated evaluation considering feedback comments and transcript context. The candidate shows {overall_seniority}-level competency with {percentage}% match to expectations."
            }
            
            result = mock_result
            st.success("✅ Mock evaluation completed!")
            
        else:
            # Real LLM call
            with st.spinner("🤖 Calling LLM for evaluation..."):
                try:
                    schema_text = get_related_class_definitions(EvaluationResult)
                    result = inference_with_fallback(
                        task_prompt=task_prompt,
                        model_schema=EvaluationResult,
                        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
                        model_schema_text=schema_text,
                        models_order=MODELS_CONFIG["default_models_order"],
                    )
                    
                    if result:
                        result = result.model_dump()
                        st.success("✅ Real LLM evaluation completed!")
                    else:
                        st.error("❌ LLM evaluation failed")
                        st.stop()
                        
                except Exception as e:
                    st.error(f"❌ LLM call failed: {str(e)}")
                    st.stop()
        
        # Display results
        st.header("📊 Evaluation Results")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🎯 Overall Assessment")
            st.metric("Overall Seniority", result["overall_seniority"].upper())
            st.metric("Match Percentage", f"{result['percentage_of_match']:.1f}%")
            
            st.subheader("📝 Overall Explanation")
            st.write(result["explanation"])
        
        with col2:
            st.subheader("📋 Per-Question Analysis")
            for q_eval in result["per_question"]:
                with st.expander(f"Question {q_eval['question_number']} - {q_eval['detected_seniority'].upper()}"):
                    st.write(f"**Expected:** {q_eval['expected_seniority']}")
                    st.write(f"**Detected:** {q_eval['detected_seniority']}")
                    st.write(f"**Explanation:** {q_eval['explanation']}")
        
        # Raw JSON output
        st.subheader("🔍 Raw JSON Result")
        st.json(result)
        
    except json.JSONDecodeError as e:
        st.error(f"❌ JSON parsing error: {str(e)}")
    except Exception as e:
        st.error(f"❌ Evaluation error: {str(e)}")

# Footer
st.markdown("---")
st.markdown("**Enhanced Interview Evaluation Tester** - Testing improved prompt integration with feedback comments and transcript data")
