from datetime import datetime
from typing import List
import psycopg2
from fastapi import APIRouter, HTTPException, Query
# from core.config import settings
# from utils.match_evaluations import evaluate_candidate, evaluate_position
from opentelemetry import trace  # NEW
from models.match_analysis_models import CompatibilityEvaluation
from utils import match_evaluations as match_functions 
# Telemetry Section
import logging
import concurrent.futures
from functools import partial
from core.config import settings
from contextlib import contextmanager
import time
from models.llm import models_pool
from controllers.candidates_controller import get_candidates_by_ids
from controllers.positions_controller import get_position_by_id
# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 
router = APIRouter()
from templates.candidates_templates.candidate_analysis import templatesObject
from utils.match_evaluations import build_compatibility_evaluation


@contextmanager
def get_cursor():
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


@router.get("/test_llm_match")
def test_llm_match(model_name: str, position_id: str, candidate_id: str = None):  # Candidate ID is optional
    """
    Endpoint to test LLM output for a single candidate match.
    """
    try:
        templatesObject.update_prompts()
        from controllers.candidates_controller import test_llm_match_logic
        return test_llm_match_logic(model_name, position_id, candidate_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_current_models")
def get_current_models():
    """
    Endpoint to get the current models available in the pool.
    """
    try:
        # Return the keys and its values from the models_pool
        models_info = {name: model.model_dump() for name, model in models_pool.items()}
        return models_info
    except Exception as e:
        logger.error(f"Error retrieving models: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving models")


# Check LLM health status
@router.get('/check_llm_health')
def check_llm_health():
    logger.info("Checking LLM health status")
    try:
        response1 = models_pool["llama4-pro"].invoke("1+1?").content
        logger.info("LLM llama is healthy")
        response2 = models_pool["gpt-4o-mini"].invoke("1+1?").content
        logger.info("LLM is gpt is healthy")

        response = f"LLM llama is healthy: {response1}, LLM gpt is healthy: {response2}"
        return {"status": "Healthy", "response": response}
    except HTTPException as http_exc:
        logger.error(f"LLM health check failed: {http_exc.detail}")
        return {"status": "Unhealthy", "error": http_exc.detail}
    except Exception as e:
        logger.error(f"LLM health check failed: {str(e)}")
        return {"status": "Unhealthy", "error": str(e)}


# Context manager to log time taken for a block of code
@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


# This endpoint matches a position with candidates or a candidate with positions.
@router.post("/match")
def execute_match(
    position_id: str = Query(None), 
    candidate_id: str = Query(None),
    limit: int = 5,
    hasFeedback: int = Query(2, description="If 0, without feedback. If 1, with feedback. If 2, both."),
    batch_mode: bool = Query(True, description="If True, analyze all candidates in one prompt. If False, analyze in parallel.")
):
    try:
        """
        Endpoint to match a position with candidates or a candidate with positions.
        - If position_id is provided, it retrieves candidates for that position.
        - If candidate_id is provided, it retrieves positions for that candidate.
        - If both are provided, it prioritizes position_id.
        Supports batch (single prompt) or parallel processing modes.
        """
        logger.info("Starting endpoint /match with position_id=%s candidate_id=%s limit=%d batch_mode=%s",
                    position_id, candidate_id, limit, batch_mode)

        with tracer.start_as_current_span("execute_match_logic") as span:
            if not position_id and not candidate_id:
                logger.warning("Neither position_id nor candidate_id provided")
                raise HTTPException(status_code=400, detail="You must provide either position_id or candidate_id.")

            # Connect to the database
            # Add a sub-span for the query
            with tracer.start_as_current_span("db_connect_and_query") as db_span:
                db_span.set_attribute("database.url", settings.DATABASE_URL)
                if position_id:
                    # Retrieve position embedding & text
                    logger.info("Retrieved embedding for position Id=%s", position_id)
                    position_embedding, processed_position = _get_position_embedding_and_text(position_id)
                    # Search for candidates 
                    results = _search_candidates_for_position_embedding(
                        position_id, position_embedding, hasFeedback, limit
                    )
                else:
                    # Retrieve candidate embedding & text
                    logger.info("Retrieved embedding for candidate Id=%s", candidate_id)
                    candidate_embedding, processed_candidate = _get_candidate_embedding_and_text(candidate_id)
                    # Search for positions
                    results = _search_positions_for_candidate_embedding(
                        candidate_id, candidate_embedding, limit
                    )

            # We process the results (another sub-span, for example, for matching logic with LLM)
            with tracer.start_as_current_span("matching_evaluation") as eval_span:
                if position_id:
                    eval_span.set_attribute("match.position_id", position_id)
                    if batch_mode:
                        return _batch_mode_analysis(results, processed_position)
                    if not batch_mode:  # Either by choice or fallback
                        return _process_candidates_parallel(results, processed_position)
                else:
                    eval_span.set_attribute("match.candidate_id", candidate_id)
                    positions_result = []
                    for r in results:
                        pos_id_db = r[0]
                        proj_id_db = r[1]
                        position_info_db = r[2]
                        similarity_score = r[3]
                        position_text = r[4]

                        analysis = match_functions.evaluate_position(
                            processed_candidate, position_text
                        )

                        positions_result.append({
                            "id": pos_id_db,
                            "proj_id": proj_id_db,
                            "position_info": position_info_db,
                            "cosine_similarity": similarity_score,
                            "analysis": dict(analysis),
                        })

                    final_match_response = {
                        "matched_positions": positions_result,
                        "processed_candidate": processed_candidate,
                        "timestamp": datetime.now(),
                    }
                    logger.info("Returning %d matched positions", len(positions_result))
                    return final_match_response
    except psycopg2.Error as db_error:
        logger.error("Database error: %s", db_error, exc_info=True)
        raise HTTPException(status_code=500, detail=f"execute_match. Database error occurred: {str(db_error)}")
    except HTTPException as http_exc:
        logger.error("HTTP Exception in endpoint /match: %s", http_exc.detail, exc_info=True)
        raise http_exc
    except Exception as e:
        logger.error("Error occurred : %s", e, exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while processing the match")


# Helper function for parallel processing
def process_candidate_parallel(result_row, processed_position):
    """
    Processes a single candidate row in parallel, evaluating compatibility with the given position.
    """
    candidate_id_db = result_row[0]
    proj_id_db = result_row[1]
    candidate_info_db = result_row[2]
    similarity_score = result_row[3]
    candidate_text = result_row[4]

    # Process this candidate
    analysis = match_functions.evaluate_candidate(
        candidate_text, 
        processed_position
    )

    return {
        "id": candidate_id_db,
        "proj_id": proj_id_db,
        "candidate_info": candidate_info_db,
        "cosine_similarity": similarity_score,
        "analysis": dict(analysis),
    }


# Custom prompt evaluation
# This endpoint evaluates a candidate's CV against a job description using a custom prompt.
@router.post("/evaluate_candidate_custom_prompt")
def evaluate_candidate_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:
    """
    Evaluates a candidate's CV against a job description using a custom prompt.
    Returns a structured analysis including compatibility percentage, recommendation, matches found, and missing requirements.
    """
    logger.info("Evaluating candidate with custom prompt")
    if not candidate_text or not processed_position:
        logger.error("Candidate text or processed position is empty")
        raise HTTPException(status_code=400, detail="Candidate text and processed position are required")
    analysis = match_functions.evaluate_candidate_custom_prompt(
        candidate_text=candidate_text,
        processed_position=processed_position
    )
    if not analysis:
        logger.error("No analysis result returned from evaluate_candidate_custom_prompt")
        raise HTTPException(status_code=404, detail="No analysis result available for the provided candidate and position")


# Helper function to get the candidate query based on feedback status
# This function returns a SQL query string based on the hasFeedback parameter.
# If hasFeedback is 0, it retrieves candidates without feedback.
def get_candidate_query(hasFeedback):
    """
    Returns a SQL query string to retrieve candidates based on the feedback status.
    Args:
        hasFeedback (int): 
            0 - retrieves candidates without feedback for the given position,
            1 - retrieves candidates with feedback for the given position,
            2 - retrieves all candidates regardless of feedback.

    Returns:
        str: SQL query string.
    """
    if hasFeedback == 0:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id AND i.position_id = %s
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) = 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    elif hasFeedback == 1:
        return """
            SELECT 
                c.id, 
                c.proj_id, 
                c.candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                c.to_be_embebbed,
                COUNT(i.id) AS num_feedbacks
            FROM 
                candidates_smarthr c
            LEFT JOIN 
                interviews i ON i.candidate_id = c.id
            WHERE 
                c.is_deleted = false 
                AND c.is_active = true  AND i.position_id = %s
            GROUP BY 
                c.id, c.proj_id, c.candidate_info, c.to_be_embebbed
            HAVING 
                COUNT(i.id) > 0
            ORDER BY 
                cosine_similarity DESC
            LIMIT %s
        """
    else:
        return """
            SELECT 
                id, 
                proj_id, 
                candidate_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                to_be_embebbed
            FROM 
                candidates_smarthr 
            Where is_deleted = false and is_active = true
            ORDER BY cosine_similarity DESC
            LIMIT %s
        """


# This function processes candidates in batch mode.
# If batch_mode is True, it processes all candidates in a single prompt.
def _batch_mode_analysis(results, processed_position):
    try:
        candidates_result = []
        # Prepare all candidates for batch analysis
        # candidates_texts = [r[4] for r in results]  # Get all processed texts
        # Attempt batch analysis
        # batch_analysis = match_functions.evaluate_candidates_batch(
        #     candidates_texts, 
        #     processed_position
        # )
        # print("Results length:", len(results))
        # candidates_analysis = batch_analysis.model_dump()
        # print("Batch analysis length:", len(candidates_analysis["candidates_analysis"]))
        # candidates_analysis["created_at"] = datetime.now()
        # candidates_analysis["updated_at"] = datetime.now()

        # # Map batch results back to individual candidates
        # num_analyses = len(candidates_analysis["candidates_analysis"])
        for idx, r in enumerate(results):
            # if idx >= num_analyses:
            #     logger.warning(f"No analysis found for candidate index {idx}, skipping this candidate.")
            #     continue  # Skip this candidate if there is no corresponding analysis

            candidate_id_db = r[0]
            proj_id_db = r[1]
            candidate_info_db = r[2]
            similarity = r[3] if len(r) > 3 else 0.0  # Default to 0.0 if not provided

            # llm_analysis = candidates_analysis["candidates_analysis"][idx]
            custom_analysis_json = match_functions.get_candidate_analysis_custom_prompt(
                candidate_text=candidate_info_db,
                processed_position=processed_position
            )
            custom_analysis_summaries = match_functions.get_candidate_analysis_custom_prompt(
                candidate_text=r[4],
                processed_position=processed_position
            )

            # Determine if the custom analysis is based on the info_db or summaries
            # Assuming compatibilityPercentage is a field in the custom_prompt_json and custom_prompt_summaries
            is_info_db = custom_analysis_json.compatibilityPercentage > custom_analysis_summaries.compatibilityPercentage

            # Append the results
            candidates_result.append({
                "id": candidate_id_db,
                "proj_id": proj_id_db,
                "candidate_info": candidate_info_db,
                "cosine_similarity": round(similarity * 100, 2) if similarity is not None else 0.0,  # Convert to percentage with two decimals
                # Build compatibility evaluation
                # "analysis": build_compatibility_evaluation(
                #     compatibility_percentage=llm_analysis.get("Score", 0) * 10,
                #     justification=llm_analysis["LLM_Analysis"]["reason"],
                #     matches_found=list(llm_analysis["LLM_Analysis"]["skill_match_analysis"].keys()),
                #     missing_requirements=llm_analysis["LLM_Analysis"]["skill_not_matched"]
                # ),
                "custom_analysis": custom_analysis_json if is_info_db else custom_analysis_summaries,
                "custom_analysis_1": custom_analysis_summaries if is_info_db else custom_analysis_json,
                "type": "info_db" if is_info_db else "summary_db",
            })
        # Add batch summary to result
        final_match_response = {
            "matched_candidates": candidates_result,
            "processed_position": processed_position,
            "timestamp": datetime.now(),
        }
        logger.info("Returning %d candidates matched in batch mode", len(candidates_result))
        return final_match_response
    except Exception as e:
        logger.warning(f"Batch analysis failed, switching to parallel processing: {str(e)}")
        # batch_mode = False  # Switch to parallel processing


# This function processes candidates in parallel.
# It uses ThreadPoolExecutor to evaluate candidates against the position in parallel.
# It returns a structured response with matched candidates and their analyses.
def _process_candidates_parallel(results, processed_position):
    # Process candidates in parallel using ThreadPoolExecutor
    with log_time_block("Parallel Candidate Analysis"):
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(results))) as executor:
            # Create a partial function with the position already set
            evaluate_func = partial(process_candidate_parallel, processed_position=processed_position)
            # Map the function to all results
            parallel_results = list(executor.map(evaluate_func, results))
            # Add results to candidates_result
            candidates_result = parallel_results
    final_match_response = {
        "matched_candidates": candidates_result,
        "processed_position": processed_position,
        "timestamp": datetime.now(),
    }
    logger.info("Returning %d candidates matched in parallel mode", len(candidates_result))
    return final_match_response


# Helper funtion to retrieve position embedding and text
# This function retrieves the position embedding and processed text from the database.
def _get_position_embedding_and_text(position_id):
    with get_cursor() as cur:
        # Retrieve position embedding & text
        logger.info("Retrieving position embedding and text for position_id=%s", position_id)
        cur.execute(
            "SELECT embedding, to_be_embebbed FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        row = cur.fetchone()
        if not row:
            logger.warning("Position not found with id=%s", position_id)
            raise HTTPException(status_code=404, detail="Position not found")

        position_embedding = row[0]
        processed_position = row[1]
    return position_embedding, processed_position


# Helper function to search candidates for a position embedding
# This function searches for candidates based on the position embedding and hasFeedback status.
def _search_candidates_for_position_embedding(position_id, position_embedding, hasFeedback, limit):
    if hasFeedback not in [0, 1, 2]:
        logger.warning("Invalid hasFeedback value: %d", hasFeedback)
        raise HTTPException(status_code=400, detail="Invalid hasFeedback value")
    # Search for candidates
    query = get_candidate_query(hasFeedback)
    if hasFeedback in [0, 1]:
        # If hasFeedback is 0 or 1, we need to filter by position_id
        with get_cursor() as cur:
            cur.execute(query, (position_embedding, position_id, limit))
            results = cur.fetchall()
    else:
        with get_cursor() as cur:
            cur.execute(query, (position_embedding, limit))
            results = cur.fetchall()
    logger.info("Candidates found: %d", len(results))
    return results


# Helper function to retrieve candidate embedding and text
# This function retrieves the candidate embedding and processed text from the database.
def _get_candidate_embedding_and_text(candidate_id):
    with get_cursor() as cur:
        cur.execute(
            "SELECT embedding, to_be_embebbed FROM candidates_smarthr WHERE id=%s AND is_deleted = false and is_active = true",
            (candidate_id,),
        )
        row = cur.fetchone()
        if not row:
            logger.warning("Candidate not found with Id=%s", candidate_id)
            raise HTTPException(status_code=404, detail="Candidate not found")

        candidate_embedding = row[0]
        processed_candidate = row[1]
    return candidate_embedding, processed_candidate


# Helper function to search positions for a candidate embedding
# This function searches for positions based on the candidate embedding.
def _search_positions_for_candidate_embedding(candidate_embedding, limit):
    with get_cursor() as cur:
        cur.execute(
            """SELECT id, proj_id, position_info, 1 - (embedding <=> %s) AS cosine_similarity, to_be_embebbed
                FROM positions_smarthr
                ORDER BY cosine_similarity DESC
                LIMIT %s
            """,
            (candidate_embedding, limit),
        )
        results = cur.fetchall()
    logger.info("Positions found: %d", len(results))
    return results


# This endpoint matches a position with candidates or a candidate with positions.
@router.get("/match/custom")
def custom_match(
    position_id: str = Query(None, description="Position ID to match candidates against"), 
    candidates_id: List[str] = Query(..., description="List of candidate IDs")
):
    try:
        logger.info("Starting endpoint /match with position_id=%s candidates_id=%s", position_id, candidates_id)
        if not position_id:
            logger.warning("Position ID is required for matching candidates")
            raise HTTPException(status_code=400, detail="Position ID is required for matching candidates.")

        if not candidates_id or len(candidates_id) == 0:
            logger.warning("Candidates IDs are required for matching")
            raise HTTPException(status_code=400, detail="Candidates IDs are required for matching.")

        # Retrieve position embedding & text
        logger.info("Retrieved embedding for position Id=%s", position_id)
        position = get_position_by_id(position_id)
        if not position:
            logger.warning("Position not found with id=%s", position_id)
            raise HTTPException(status_code=404, detail="Position not found with the provided ID.")

        position_embedding, processed_position = _get_position_embedding_and_text(position_id)
        # Search for candidates
        candidates = get_candidates_by_ids(candidates_id, position_embedding)

        if not candidates:
            logger.warning("No candidates found with provided IDs: %s", candidates_id)
            raise HTTPException(status_code=404, detail="No candidates found with provided IDs.")
        # Ensure candidates is a list of tuples with the expected structure
        if not all(isinstance(c, tuple) and len(c) >= 6 for c in candidates):
            logger.error("Invalid candidates data structure: %s", candidates)
            raise HTTPException(status_code=400, detail="Invalid candidates data structure. Expected a list of tuples with at least 6 elements each.")
        # Log the number of candidates found
        logger.info("Found %d candidates for position_id=%s", len(candidates), position_id)

        # Prepare all candidates for batch analysis
        # candidates_texts = [r[4] for r in candidates]  # Get all processed texts
        # Attempt batch analysis
        # logger.info("Attempting batch analysis for %d candidates", len(candidates_texts))
        # batch_analysis = match_functions.evaluate_candidates_batch(
        #     candidates_texts, 
        #     processed_position
        # )

        # candidates_analysis = batch_analysis.model_dump()
        # candidates_analysis["created_at"] = datetime.now()
        # candidates_analysis["updated_at"] = datetime.now()
        # Initialize candidates_result list
        logger.info("Processing candidates in custom match mode")
        candidates_result = []
        # Map batch results back to individual candidates
        for idx, r in enumerate(candidates):
            print("Mapping batch results back to individual candidates:", len(r))

            candidate_id_db = r[0]
            candidate_info_db = r[1]
            candidate_embedding = r[2]
            candidate_sparse_embedding = r[3]
            candidate_to_be_embebbed = r[4]
            proj_id_db = r[5]
            similarity = r[6] if len(r) > 6 else 0.0  # Default to 0.0 if not provided

            # Compute cosine similarity in Python (assuming numpy is imported as np)
            # similarity_score = 1 - np.dot(candidate_embedding, position_embedding) / (np.linalg.norm(candidate_embedding) * np.linalg.norm(position_embedding))

            # Check if analysis exists for this candidate
            # if idx >= len(candidates_analysis["candidates_analysis"]):
            #     logger.warning("No analysis found for candidate index %d", idx)
            #     raise HTTPException(status_code=404, detail=f"No analysis found for candidate index {idx}.")
            # # Get analysis for this candidate
            # llm_analysis = candidates_analysis["candidates_analysis"][idx]

            custom_analysis_0 = match_functions.get_candidate_analysis_custom_prompt(
                candidate_text=candidate_info_db,
                processed_position=position.position_info
            )
            custom_analysis_1 = match_functions.get_candidate_analysis_custom_prompt(
                candidate_text=candidate_to_be_embebbed,
                processed_position=processed_position
            )

            is_info_db = custom_analysis_0.compatibilityPercentage > custom_analysis_1.compatibilityPercentage
            # Evaluate candidate against position
            candidates_result.append({
                "id": candidate_id_db,
                "proj_id": proj_id_db,
                "candidate_info": candidate_info_db,
                "cosine_similarity": round(similarity * 100, 2) if similarity is not None else 0.0,  # Convert to percentage with two decimals
                # Build compatibility evaluation
                # "analysis": build_compatibility_evaluation(
                #     compatibility_percentage=llm_analysis.get("Score", 0) * 10,
                #     justification=llm_analysis["LLM_Analysis"]["reason"],
                #     matches_found=list(llm_analysis["LLM_Analysis"]["skill_match_analysis"].keys()),
                #     missing_requirements=llm_analysis["LLM_Analysis"]["skill_not_matched"]
                # ),
                "custom_analysis": custom_analysis_0 if is_info_db else custom_analysis_1,
                "custom_analysis_1": custom_analysis_1 if is_info_db else custom_analysis_0,
                "type": "info_db" if is_info_db else "summary_db",
            })

        # build the final response
        logger.info("Finalizing match response with %d candidates", len(candidates_result))
        final_match_response = {
            "matched_candidates": candidates_result,
            "processed_position": processed_position,
            "timestamp": datetime.now(),
        }
        logger.info("Returning %d candidates matched in custom mode", len(candidates_result))
        return final_match_response
    except psycopg2.Error as db_error:
        logger.error("Database error: %s", db_error, exc_info=True)
        raise HTTPException(status_code=500, detail=f"Custom Match. execute_match. Database error occurred: {str(db_error)}")
    except HTTPException as http_exc:
        logger.error("Custom Match. HTTP Exception in endpoint /match/custom: %s", http_exc.detail, exc_info=True)
        raise http_exc
    except Exception as e:
        logger.error("Custom Match. Error occurred : %s", e, exc_info=True)
        raise HTTPException(status_code=500, detail="Custom Match. An error occurred while processing the match.")


# Return environment variables for debugging
@router.get("/env_variables")
def get_env_variables():
    import os
    env_vars = {
        "TENANT_ID": os.getenv("TENANT_ID"),
        "CLIENT_ID_SMART_HR": os.getenv("CLIENT_ID_SMART_HR"),
        "CLIENT_ID_POWER_APP": os.getenv("CLIENT_ID_POWER_APP"),
        "CLIENT_ID_FLOW_HR": os.getenv("CLIENT_ID_FLOW_HR"),
        "POSTGRES_HOST": os.getenv("POSTGRES_HOST"),
        "POSTGRES_DB": os.getenv("POSTGRES_DB"),
        "POSTGRES_USER": os.getenv("POSTGRES_USER"),
        "LUMUS_API_URL": os.getenv("LUMUS_API_URL"),
        "LUMUS_API_TIMEOUT": os.getenv("LUMUS_API_TIMEOUT"),
        "DEFAULT_MODELS_ORDER": os.getenv("DEFAULT_MODELS_ORDER"),
        "POSITION_MATCH_MODELS_ORDER": os.getenv("POSITION_MATCH_MODELS_ORDER"),
        # Add other relevant environment variables as needed
    }
    return env_vars
