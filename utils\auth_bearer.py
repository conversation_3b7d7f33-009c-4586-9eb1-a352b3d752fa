from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from utils.jwt_utils import validate_token


class JWTBearer(HTTPBearer):
    def __init__(self, auto_error: bool = True):
        super(J<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(J<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=403, detail="Invalid authentication scheme.")
            if not self.verify_jwt(credentials.credentials):
                raise HTTPException(status_code=403, detail="Invalid token or expired token.")
            return credentials.credentials
        else:
            raise HTTPException(status_code=403, detail="Invalid authorization code.")

    def verify_jwt(self, jwtoken: str) -> bool:
        isTokenValid: bool = False

        try:
            payload = validate_token(jwtoken)
        except:
            payload = None
        if payload:
            isTokenValid = True

        return isTokenValid
